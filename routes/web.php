<?php

use App\Http\Controllers\BankController;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\HookController;
use App\Http\Controllers\QRController;
use App\Http\Controllers\UserController;
use App\Models\BankTransaction;
use App\Models\Proxy;
use App\Services\BIDVOpenAPIService;
use GuzzleHttp\Client;
use Illuminate\Auth\Events\PasswordReset;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Password;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Str;
use Jose\Component\Core\AlgorithmManager;
use Jose\Component\Core\JWK;
use Jose\Component\Encryption\Algorithm\ContentEncryption\A128GCM;
use Jose\Component\Encryption\Algorithm\ContentEncryption\A256CBCHS512;
use Jose\Component\Encryption\Algorithm\KeyEncryption\A128KW;
use Jose\Component\Encryption\Algorithm\KeyEncryption\A256KW;
use Jose\Component\Encryption\Algorithm\KeyEncryption\PBES2HS256A128KW;
use Jose\Component\Encryption\Compression\CompressionMethodManager;
use Jose\Component\Encryption\Compression\Deflate;
use Jose\Component\Encryption\JWEBuilder;
use Jose\Component\Encryption\Serializer\CompactSerializer;
use Jose\Component\Encryption\Serializer\JSONGeneralSerializer;

Route::get('/login', [UserController::class, 'login'])->name('login');
Route::post('/login', [UserController::class, 'postLogin'])->name('post.login');
Route::get('/register', [UserController::class, 'register'])->name('get.register');
Route::post('/register', [UserController::class, 'postRegister'])->name('post.register');

Route::get('/forgot-password', function () {
    return view('auth.forgot-password');
})->middleware('guest')->name('password.request');
Route::post('/forgot-password', function (Request $request) {
    $request->validate(['email' => 'required|email']);

    $status = Password::sendResetLink(
        $request->only('email')
    );
    return $status === Password::RESET_LINK_SENT
        ? back()->with(['status' => 'Chúng tôi đã gửi email liên kết đặt lại mật khẩu của bạn!'])
        : back()->withErrors(['email' => 'Chúng tôi không thể tìm thấy người dùng có địa chỉ email đó.']);
})->middleware('guest')->name('password.email');

Route::get('/reset-password/{token}', function ($token, Request $request) {
    return view('auth.reset-password', ['token' => $token, 'email' => $request->email]);
})->middleware('guest')->name('password.reset');



Route::post('/reset-password', function (Request $request) {
    $request->validate([
        'token' => 'required',
        'email' => 'required|email',
        'password' => 'required|min:8|confirmed',
    ]);

    $status = Password::reset(
        $request->only('email', 'password', 'password_confirmation', 'token'),
        function ($user, $password) use ($request) {
            $user->forceFill([
                'password' => Hash::make($password)
            ])->save();

            $user->setRememberToken(Str::random(60));
            event(new PasswordReset($user));
        }
    );

    return $status == Password::PASSWORD_RESET
        ? redirect()->route('login')->with('register_completed', 'Đổi mật khẩu thành công!')
        : back()->withErrors(['email' => [__($status)]]);
})->middleware('guest')->name('password.update');


Route::group(["middleware" => "auth"], function () {
    Route::get('', [HomeController::class, 'dashboard'])->name('dashboard');
    Route::get('/add/business/bidv', [BankController::class, 'addBusinessBidv'])->name('bank.add.business.bidv');
    Route::get('/add/personal/vcb', [BankController::class, 'addVcb'])->name('bank.add.vcb');
    Route::get('/add/personal/acb', [BankController::class, 'addAcb'])->name('bank.add.acb');
    Route::get('/add/personal/mb', [BankController::class, 'addMb'])->name('bank.add.mb');
    Route::get('/add/personal/icb', [BankController::class, 'addIcb'])->name('bank.add.icb'); //vietinbank
    // Route::get('/add/personal/bidv', [BankController::class, 'addBidv'])->name('bank.add.bidv');
    Route::get('/accounts/vcb', [BankController::class, 'accountsVcb'])->name('bank.accounts.vcb');
    Route::get('/accounts/acb', [BankController::class, 'accountsAcb'])->name('bank.accounts.acb');
    Route::get('/accounts/mb', [BankController::class, 'accountsMb'])->name('bank.accounts.mb');
    Route::get('/accounts/icb', [BankController::class, 'accountsIcb'])->name('bank.accounts.icb');
    Route::get('/accounts/business/bidv', [BankController::class, 'accountsBusinessBidv'])->name('bank.accounts.business.bidv');
    Route::get('/accounts/business/bidv/detail/{id}', [BankController::class, 'detailBusinessBidv'])->name('detail.business.bidv');
    Route::get('/accounts/business/bidv/detail/{id}/add-va', [BankController::class, 'addVaNumberBidv'])->name('bank.add.va.number.bidv');
    // Route::get('/accounts/bidv', [BankController::class, 'accountsBidv'])->name('bank.accounts.bidv');
    Route::get('/accounts/vcb/{id}', [BankController::class, 'editAccountVcb'])->name('bank.edit.vcb');
    Route::get('/accounts/acb/{id}', [BankController::class, 'editAccountAcb'])->name('bank.edit.acb');
    Route::get('/accounts/mb/{id}', [BankController::class, 'editAccountMb'])->name('bank.edit.mb');
    Route::get('/accounts/icb/{id}', [BankController::class, 'editAccountIcb'])->name('bank.edit.icb');
    // Route::get('/accounts/bidv/{id}', [BankController::class, 'editAccountBidv'])->name('bank.edit.bidv');
    Route::get('/hooks', [HookController::class, 'index'])->name('hook.index');
    Route::get('/hooks/add', [HookController::class, 'add'])->name('hook.add');
    Route::get('/hooks/logs', [HookController::class, 'logs'])->name('hook.logs');
    Route::get('/hooks/embed/{id}', [HookController::class, 'embed'])->name('hook.embed');

    //unsubscribe
    Route::get('/accounts/mb/{id}/unsubscribe', [BankController::class, 'unsubscribeMB'])->name('bank.mb.unsubscribe');


    Route::get('/reports', [BankController::class, 'reports'])->name('report');
    Route::get('/qr-generate', [QRController::class, 'generate'])->name('qr-generate');
    Route::get('/naptien', [HomeController::class, 'naptien'])->name('naptien');
    Route::get('/deposit-history', [HomeController::class, 'depositHistory'])->name('depositHistory');
    Route::get('/upgrade-plan', [HomeController::class, 'upgradePlan'])->name('upgradePlan');
    Route::post('/renew-package', [HomeController::class, 'renewPackage'])->name('renewPackage');

    Route::get('/logout', [UserController::class, 'logout'])->name('logout');
    Route::delete('/bank/va/delete/{id}', [BankController::class, 'deleteVA'])->name('bank.delete.va');
});

Route::get('quicklink/{bank_code}/{account_no}/{account_name}', [QRController::class, 'quicklink'])->name('quicklink');
Route::get('s/{slug}', [QRController::class, 'vietqrStore'])->name('vietqrStore');
// Route::get('test', function () {
//     $proxy = Proxy::where('status', 1)->first();
//     $client = new Client([
//         'proxy' => $proxy->proxy ?? ''
//     ]);
//     $res = $client->get('https://api.myip.com');
//     dd($res->getBody()->getContents());
// });

// Route::get('qr-haravan', function () {
//     return view('haravan');
// });

Route::get('test', function () {
    $sv = new BIDVOpenAPIService();
    // $token = $sv->genToken();

    $jwk = new JWK([
        'kty' => 'oct',
        'k' => rtrim(strtr(base64_encode(random_bytes(32)), '+/', '-_'), '='), // Your 256-bit key
    ]);

    $payload = json_encode([
        'serviceId' => '27B001',
        'code' => '123456',
        'name' => 'CONG TY CP TAP DOAN TINO',
        'serviceCode' => 'V4TINO',
    ]);
    dump($payload);
    // The key encryption algorithm manager with the A256KW algorithm.
    $keyEncryptionAlgorithmManager = new AlgorithmManager([
        new A256KW(),
    ]);

    // The content encryption algorithm manager with the A256CBC-HS256 algorithm.
    $contentEncryptionAlgorithmManager = new AlgorithmManager([
        new A128GCM(),
    ]);
    // The compression method manager with the DEF (Deflate) method.
    $compressionMethodManager = new CompressionMethodManager([
        new Deflate(),
    ]);

    $jweBuilder = new JWEBuilder(
        $keyEncryptionAlgorithmManager,
        $contentEncryptionAlgorithmManager,
        $compressionMethodManager
    );
    $jwe = $jweBuilder
        ->create()              // We want to create a new JWE
        ->withPayload($payload) // We set the payload
        ->withSharedProtectedHeader([
            'alg' => 'A256KW',        // Key Encryption Algorithm
            'enc' => 'A128GCM', // Content Encryption Algorithm
            //'zip' => 'DEF'            // Not recommended.
        ])
        ->addRecipient($jwk)    // We add a recipient (a shared key or public key).
        ->build();
    $serializer = new JSONGeneralSerializer();
    $json = $serializer->serialize($jwe, 0);
    dump($json);

    $serializer = new CompactSerializer(); // The serializer

    $token = $serializer->serialize($jwe, 0);

    // Print the token
    dd($token);
});
